"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AppShell, Sidebar, Navbar, NavigationMenu } from "@/components/ui/app-shell";
import {
  Home,
  Users,
  Settings,
  Calendar,
  FileText,
  BarChart,
  LogOut,
  User,
  Bell,
} from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function RbacAppShellPage() {
  // Navigation items for the sidebar
  const navigationItems = [
    {
      title: "Dashboard",
      href: "#dashboard",
      icon: Home,
      active: true,
    },
    {
      title: "Users",
      href: "#users",
      icon: Users,
    },
    {
      title: "Calendar",
      href: "#calendar",
      icon: Calendar,
    },
    {
      title: "Reports",
      href: "#reports",
      icon: FileText,
    },
    {
      title: "Analytics",
      href: "#analytics",
      icon: BarChart,
    },
    {
      title: "Settings",
      href: "#settings",
      icon: Settings,
    },
  ];

  // Create a user menu component
  const UserMenu = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/placeholder-avatar.jpg" alt="John Doe" />
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          <div>
            <p className="font-medium">John Doe</p>
            <p className="text-xs text-muted-foreground"><EMAIL></p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Logout</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <div className="space-y-12">
      <h2 className="text-2xl font-semibold mb-4">RBAC App Shell</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">New Sidebar Component</h3>
        <Card>
          <CardHeader>
            <CardTitle>Sidebar Component</CardTitle>
            <CardDescription>Navigation sidebar with RBAC support</CardDescription>
          </CardHeader>
          <CardContent className="h-[400px] border rounded-md overflow-hidden">
            <Sidebar title="My App">
              <NavigationMenu items={navigationItems} />
            </Sidebar>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<Sidebar title="My App">
  <NavigationMenu items={navigationItems} />
</Sidebar>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">New Navbar Component</h3>
        <Card>
          <CardHeader>
            <CardTitle>Navbar Component</CardTitle>
            <CardDescription>Top navigation bar with left and right sections</CardDescription>
          </CardHeader>
          <CardContent className="border rounded-md overflow-hidden">
            <Navbar
              left={<h2 className="text-xl font-bold">Si Simple</h2>}
              right={
                <div className="flex items-center gap-4">
                  <ThemeToggle />
                  <Separator orientation="vertical" className="h-8" />
                  <UserMenu />
                </div>
              }
            />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<Navbar
  left={<h2 className="text-xl font-bold">Si Simple</h2>}
  right={
    <div className="flex items-center gap-4">
      <ThemeToggle />
      <Separator orientation="vertical" className="h-8" />
      <UserMenu />
    </div>
  }
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Complete RBAC App Shell</h3>
        <Card>
          <CardHeader>
            <CardTitle>App Shell Component</CardTitle>
            <CardDescription>Combined sidebar and navbar with RBAC support</CardDescription>
          </CardHeader>
          <CardContent className="h-[500px] border rounded-md overflow-hidden">
            <AppShell
              sidebar={
                <Sidebar title="My App">
                  <NavigationMenu items={navigationItems} />
                </Sidebar>
              }
              navbar={
                <Navbar
                  left={<h2 className="text-xl font-bold">Si Simple</h2>}
                  right={
                    <div className="flex items-center gap-4">
                      <ThemeToggle />
                      <Separator orientation="vertical" className="h-8" />
                      <UserMenu />
                    </div>
                  }
                />
              }
            >
              <div className="p-6">
                <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
                <p className="mb-4">Welcome to the application dashboard.</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[1, 2, 3].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <CardTitle>Card {i}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p>This is a sample card in the dashboard.</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </AppShell>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<AppShell
  sidebar={
    <Sidebar title="My App">
      <NavigationMenu items={navigationItems} />
    </Sidebar>
  }
  navbar={
    <Navbar
      left={<h2 className="text-xl font-bold">Si Simple</h2>}
      right={
        <div className="flex items-center gap-4">
          <ThemeToggle />
          <Separator orientation="vertical" className="h-8" />
          <UserMenu />
        </div>
      }
    />
  }
>
  <div className="p-6">
    <h1>Main Content</h1>
    {/* Your application content goes here */}
  </div>
</AppShell>`}
            </pre>
          </CardFooter>
        </Card>
      </section>
    </div>
  );
}
