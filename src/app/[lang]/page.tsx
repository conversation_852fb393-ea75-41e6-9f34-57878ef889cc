import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ThemeToggle } from "@/components/theme-toggle";
import { LanguageToggle } from "@/components/language-toggle";
import { i18n } from "@/lib/i18n/services/I18nService";
import { H1, H2, H3, P, Lead } from "@/components/typography";
import {
  ArrowRight,
  Users,
  Calendar,
  FileText,
  Shield,
  Clock,
  CheckCircle,
  UserCheck,
  Sparkles,
  Heart,
  Star,
} from "lucide-react";

export default async function Home({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="flex flex-col items-center min-h-screen p-4 max-w-7xl mx-auto">
      <div className="absolute top-4 right-4 flex items-center gap-2">
        <LanguageToggle currentLang={lang} />
        <ThemeToggle />
      </div>

      {/* Hero Section */}
      <div className="w-full py-16 md:py-24 lg:py-32">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Trust Badge */}
            <div className="inline-flex items-center rounded-full border px-4 py-2 text-sm text-muted-foreground">
              <Sparkles className="mr-2 h-4 w-4" />
              {dictionary.home.hero.trustedBy}
            </div>

            {/* Main Heading */}
            <div className="space-y-4">
              <H1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                {dictionary.home.hero.title}
              </H1>
              <H2 className="text-xl font-medium text-muted-foreground sm:text-2xl md:text-3xl max-w-[800px]">
                {dictionary.home.hero.subtitle}
              </H2>
            </div>

            {/* Description */}
            <Lead className="max-w-[700px] text-muted-foreground text-lg md:text-xl leading-relaxed">
              {dictionary.home.hero.description}
            </Lead>

            {/* CTA Buttons */}
            <div className="flex flex-col gap-4 min-[400px]:flex-row">
              <Button size="lg" className="text-lg px-8 py-6" asChild>
                <Link href={`/${lang}/auth/signin`}>
                  {dictionary.home.hero.primaryCta}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-6" asChild>
                <Link href={`/${lang}/docs/design-system`}>
                  {dictionary.home.hero.secondaryCta}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="w-full py-16 md:py-24 lg:py-32 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-16">
            <div className="space-y-4">
              <H2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                {dictionary.home.features.title}
              </H2>
              <P className="max-w-[700px] text-muted-foreground text-lg md:text-xl">
                {dictionary.home.features.subtitle}
              </P>
            </div>
          </div>

          <div className="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {/* Contact Management */}
            <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-background/60 backdrop-blur">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500/10 to-blue-600/10 group-hover:from-blue-500/20 group-hover:to-blue-600/20 transition-all duration-300">
                  <Users className="h-10 w-10 text-blue-600" />
                </div>
                <CardTitle className="text-xl mb-2">
                  {dictionary.home.features.contactManagement.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <P className="text-muted-foreground leading-relaxed">
                  {dictionary.home.features.contactManagement.description}
                </P>
              </CardContent>
            </Card>

            {/* Scheduling */}
            <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-background/60 backdrop-blur">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-green-500/10 to-green-600/10 group-hover:from-green-500/20 group-hover:to-green-600/20 transition-all duration-300">
                  <Calendar className="h-10 w-10 text-green-600" />
                </div>
                <CardTitle className="text-xl mb-2">
                  {dictionary.home.features.scheduling.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <P className="text-muted-foreground leading-relaxed">
                  {dictionary.home.features.scheduling.description}
                </P>
              </CardContent>
            </Card>

            {/* Case Management */}
            <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-background/60 backdrop-blur">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500/10 to-purple-600/10 group-hover:from-purple-500/20 group-hover:to-purple-600/20 transition-all duration-300">
                  <FileText className="h-10 w-10 text-purple-600" />
                </div>
                <CardTitle className="text-xl mb-2">
                  {dictionary.home.features.caseManagement.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <P className="text-muted-foreground leading-relaxed">
                  {dictionary.home.features.caseManagement.description}
                </P>
              </CardContent>
            </Card>

            {/* Security */}
            <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-background/60 backdrop-blur">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-orange-500/10 to-orange-600/10 group-hover:from-orange-500/20 group-hover:to-orange-600/20 transition-all duration-300">
                  <Shield className="h-10 w-10 text-orange-600" />
                </div>
                <CardTitle className="text-xl mb-2">
                  {dictionary.home.features.security.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <P className="text-muted-foreground leading-relaxed">
                  {dictionary.home.features.security.description}
                </P>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="w-full py-16 md:py-24 lg:py-32">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-16">
            <div className="space-y-4">
              <H2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                {dictionary.home.benefits.title}
              </H2>
              <P className="max-w-[700px] text-muted-foreground text-lg md:text-xl">
                {dictionary.home.benefits.subtitle}
              </P>
            </div>
          </div>

          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-3">
            {/* Efficiency */}
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-emerald-500/10 to-emerald-600/10">
                <Clock className="h-8 w-8 text-emerald-600" />
              </div>
              <H3 className="text-xl font-semibold">{dictionary.home.benefits.efficiency.title}</H3>
              <P className="text-muted-foreground leading-relaxed">
                {dictionary.home.benefits.efficiency.description}
              </P>
            </div>

            {/* Compliance */}
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-500/10 to-blue-600/10">
                <CheckCircle className="h-8 w-8 text-blue-600" />
              </div>
              <H3 className="text-xl font-semibold">{dictionary.home.benefits.compliance.title}</H3>
              <P className="text-muted-foreground leading-relaxed">
                {dictionary.home.benefits.compliance.description}
              </P>
            </div>

            {/* Collaboration */}
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-purple-500/10 to-purple-600/10">
                <UserCheck className="h-8 w-8 text-purple-600" />
              </div>
              <H3 className="text-xl font-semibold">
                {dictionary.home.benefits.collaboration.title}
              </H3>
              <P className="text-muted-foreground leading-relaxed">
                {dictionary.home.benefits.collaboration.description}
              </P>
            </div>
          </div>
        </div>
      </div>

      {/* Documentation Section */}
      <div className="w-full py-16 md:py-24 lg:py-32 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-16">
            <div className="space-y-4">
              <H2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Developer Resources
              </H2>
              <P className="max-w-[700px] text-muted-foreground text-lg md:text-xl">
                Comprehensive documentation for developers and AI assistants
              </P>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2">
            <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-background/60 backdrop-blur">
              <CardHeader className="text-center">
                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-indigo-500/10 to-indigo-600/10 group-hover:from-indigo-500/20 group-hover:to-indigo-600/20 transition-all duration-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-10 w-10 text-indigo-600"
                  >
                    <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2" />
                    <path d="M18 14h-8" />
                    <path d="M15 18h-5" />
                    <path d="M10 6h8v4h-8V6Z" />
                  </svg>
                </div>
                <CardTitle className="text-xl mb-2">Design System Guide</CardTitle>
                <CardDescription>
                  Comprehensive guide to the design system for developers and AI assistants
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <P className="text-muted-foreground leading-relaxed">
                  Explore our design system documentation, including component usage, layout
                  patterns, typography guidelines, and more. This guide helps maintain consistency
                  across the application.
                </P>
              </CardContent>
              <CardFooter className="pt-6">
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/${lang}/docs/design-system/ai-guide`}>
                    View Design System Guide <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-background/60 backdrop-blur">
              <CardHeader className="text-center">
                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-slate-500/10 to-slate-600/10 group-hover:from-slate-500/20 group-hover:to-slate-600/20 transition-all duration-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-10 w-10 text-slate-600"
                  >
                    <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                    <path d="M9 18c-4.51 2-5-2-7-2" />
                  </svg>
                </div>
                <CardTitle className="text-xl mb-2">GitHub Workflow Guide</CardTitle>
                <CardDescription>Detailed guide to our GitHub workflow methodology</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <P className="text-muted-foreground leading-relaxed">
                  Learn about our hierarchical issue structure, branching strategy, PR workflow, and
                  helper scripts. This guide is especially useful for AI assistants working on the
                  project.
                </P>
              </CardContent>
              <CardFooter className="pt-6">
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/${lang}/docs/github-workflow`}>
                    View GitHub Workflow Guide <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>

      {/* Final CTA Section */}
      <div className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            <div className="space-y-4">
              <H2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                {dictionary.home.cta.title}
              </H2>
              <P className="max-w-[700px] text-muted-foreground text-lg md:text-xl leading-relaxed">
                {dictionary.home.cta.description}
              </P>
            </div>

            <div className="flex flex-col gap-4 min-[400px]:flex-row">
              <Button size="lg" className="text-lg px-8 py-6" asChild>
                <Link href={`/${lang}/auth/signin`}>
                  <Heart className="mr-2 h-5 w-5" />
                  {dictionary.home.cta.button}
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-6" asChild>
                <Link href={`/${lang}/contact`}>{dictionary.home.cta.contact}</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="w-full py-8 border-t bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center gap-4 md:flex-row md:gap-6">
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-primary" />
              <P className="text-center text-sm text-muted-foreground font-medium">Si Simple</P>
            </div>
            <P className="text-center text-sm text-muted-foreground">
              © 2025 Si Simple. All rights reserved.
            </P>
          </div>
        </div>
      </footer>
    </div>
  );
}
