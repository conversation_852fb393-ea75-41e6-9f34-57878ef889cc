import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { viewObservationNote } from "../../actions/view";
import { ROUTES } from "../../lib/config/domain";
import EditObservationNoteForm from "./components/EditObservationNoteForm";

interface EditObservationNotePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function EditObservationNotePage({ params }: EditObservationNotePageProps) {
  const { lang, id } = await params;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Suspense fallback={<div>Loading note...</div>}>
        <EditObservationNoteWrapper lang={lang} id={id} />
      </Suspense>
    </div>
  );
}

async function EditObservationNoteWrapper({ lang, id }: { lang: string; id: string }) {
  const result = await viewObservationNote({ success: false, error: "", data: null }, { id });

  if (!result.success || !result.data) {
    if (result.error?.includes("not found")) {
      notFound();
    }
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">Error loading note: {result.error}</p>
          <Button variant="outline" asChild className="mt-4">
            <Link href={`/${lang}${ROUTES.LIST}`}>Back to Notes</Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  const note = result.data;

  return (
    <>
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${lang}${ROUTES.VIEW(id)}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Note
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Observation Note</h1>
          <p className="text-muted-foreground">Update the observation note details</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Edit Note: {note.title}</CardTitle>
          <CardDescription>
            Make changes to the observation note below. The appointment cannot be changed after
            creation.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EditObservationNoteForm lang={lang} note={note} />
        </CardContent>
      </Card>
    </>
  );
}
