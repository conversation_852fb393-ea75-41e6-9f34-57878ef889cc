import { Suspense } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, FileText, Calendar, User } from "lucide-react";
import { listObservationNotes } from "./actions/list";
import { ROUTES } from "./lib/config/domain";
import { formatDistanceToNow } from "date-fns";

export default async function ObservationNotesPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Observation Notes</h1>
          <p className="text-muted-foreground">
            Manage observation notes for appointments related to case files
          </p>
        </div>
        <Button asChild>
          <Link href={`./create`}>
            <Plus className="h-4 w-4 mr-2" />
            New Note
          </Link>
        </Button>
      </div>

      {/* Notes List */}
      <Suspense fallback={<div>Loading notes...</div>}>
        <ObservationNotesList />
      </Suspense>
    </div>
  );
}

async function ObservationNotesList() {
  const result = await listObservationNotes();

  if (!result.success) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">Error loading notes: {result.error}</p>
        </CardContent>
      </Card>
    );
  }

  const notes = result.data || [];

  if (notes.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No observation notes yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first observation note for an appointment related to a case file.
          </p>
          <Button asChild>
            <Link href={ROUTES.CREATE}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Note
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      {notes.map((note) => (
        <Card key={note.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div className="space-y-1">
                <CardTitle className="text-lg">
                  <Link href={ROUTES.VIEW(note.id)} className="hover:underline">
                    {note.title}
                  </Link>
                </CardTitle>
                <CardDescription>
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {note.appointment_title} -{" "}
                      {new Date(note.appointment_date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      Case: {note.case_number}
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {note.created_by_email}
                    </div>
                  </div>
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={
                    note.status === "approved"
                      ? "default"
                      : note.status === "pending_approval"
                        ? "secondary"
                        : note.status === "rejected"
                          ? "destructive"
                          : "outline"
                  }
                >
                  {note.status.replace("_", " ")}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div
                className="text-sm text-muted-foreground line-clamp-2"
                dangerouslySetInnerHTML={{
                  __html: note.content.replace(/<[^>]*>/g, "").substring(0, 150) + "...",
                }}
              />
              <div className="flex justify-between items-center text-xs text-muted-foreground">
                <span>
                  Created {formatDistanceToNow(new Date(note.created_at), { addSuffix: true })}
                </span>
                {note.updated_at !== note.created_at && (
                  <span>
                    Updated {formatDistanceToNow(new Date(note.updated_at), { addSuffix: true })}
                  </span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
