import { notFound } from "next/navigation";
import { RequestDetail } from "../../components/RequestDetail";
import { i18n } from "@/lib/i18n/services/I18nService";
import { getRequestWithAllRelations } from "../../actions";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, Home, FileText, Calendar, User, Tag, Clock, MapPin, Sparkles, ArrowRight, Activity } from "lucide-react";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import DOMAIN_CONFIG from "../../lib/config/domain";
import { RequestStatusBadge } from "../../components/RequestStatusBadge";
import { RequestStatusActions } from "../../components/RequestStatusActions";
import { H1, Lead } from "@/components/typography";

interface ViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

// Helper function to format dates in a human-friendly way based on language
const formatDate = (dateString: string, lang: string): string => {
  try {
    const date = new Date(dateString);

    // Use the user's language for date formatting
    const locale = lang === "fr" ? "fr-CA" : "en-US";

    // Format based on locale with a more human-friendly format
    return new Intl.DateTimeFormat(locale, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: lang !== "fr", // French typically uses 24-hour format
    }).format(date);
  } catch (error) {
    return dateString;
  }
};

/**
 * View page for a specific Request item
 * Uses the RequestDetail component to display the request details
 */
export default async function ViewPage({ params }: ViewPageProps) {
  // Await the params
  const { lang, id } = await params;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);
  const requestDictionary = dictionary.request;

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Get the request data with all relations
  const response = await getRequestWithAllRelations(id);

  // If the request doesn't exist, show the not found page
  if (!response.success || !response.data) {
    notFound();
  }

  const request = response.data;

  // Check if the user has permission to edit the request
  // For now, we'll just check if the user is the creator or if they're in the same organization
  const canEdit =
    request.requester_id === user.id ||
    request.organization_id === (await auth.getCurrentUserOrganizationId());

  // Define permissions for request actions
  // For now, allow all actions if the user can edit the request
  const permissions = {
    canApprove: canEdit,
    canReject: canEdit,
    canComplete: canEdit,
    canWaitlist: canEdit,
    canRequest: canEdit,
  };

  // Format dates
  const createdAt = request.created_at ? formatDate(request.created_at, lang) : "-";

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary/10 via-primary/5 to-background border border-primary/20 p-8">
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />
        <div className="relative">
          {/* Breadcrumb */}
          <div className="mb-6">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href={`/${lang}/protected/dashboard`}>
                    <Home className="h-4 w-4" />
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href={`/${lang}${DOMAIN_CONFIG.basePath}/list`}>
                    {requestDictionary?.title || "Requests"}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink>{requestDictionary?.viewTitle || "View"}</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            {/* Request Info */}
            <div className="flex-1 space-y-4">
              <div className="flex items-center gap-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10 border border-primary/20">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <H1 className="text-3xl font-bold tracking-tight">
                      {request.reference_number || `REQ-${request.id.substring(0, 6)}`}
                    </H1>
                    <RequestStatusBadge
                      status={request.status}
                      dictionary={dictionary}
                      size="lg"
                      showTooltip
                    />
                  </div>
                  <p className="text-lg text-muted-foreground">
                    {request.service?.name || (lang === 'fr' ? 'Demande de service' : 'Service Request')}
                  </p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="flex flex-wrap gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>
                    {request.requester?.name || request.requester?.email?.split("@")[0] || (lang === 'fr' ? 'Demandeur' : 'Requester')}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {lang === 'fr' ? 'Créé le' : 'Created'} {createdAt.split(",")[0]}
                  </span>
                </div>
                {request.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>{request.location.name}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  <span>
                    {lang === 'fr' ? 'Mis à jour' : 'Updated'} {new Date(request.updated_at).toLocaleDateString(lang === 'fr' ? 'fr-CA' : 'en-CA')}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button variant="outline" className="group" asChild>
                <Link href={`/${lang}${DOMAIN_CONFIG.basePath}/list`}>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  {requestDictionary?.backToList || "Back to List"}
                </Link>
              </Button>
              <RequestStatusActions
                request={request}
                dictionary={dictionary}
                permissions={permissions}
                lang={lang}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Key Information Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Reference Number */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-blue-50/30 dark:to-blue-950/30">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-950/50">
                <Tag className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {lang === 'fr' ? 'Numéro de référence' : 'Reference Number'}
                </CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-blue-700 truncate">
              {request.reference_number || `REQ-${request.id.substring(0, 6)}`}
            </div>
          </CardContent>
        </Card>

        {/* Service */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-green-50/30 dark:to-green-950/30">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-950/50">
                <FileText className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {requestDictionary?.fields?.serviceType || (lang === 'fr' ? 'Service' : 'Service')}
                </CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-green-700 truncate">
              {request.service?.name || requestDictionary?.notSpecified || (lang === 'fr' ? 'Non spécifié' : 'Not specified')}
            </div>
          </CardContent>
        </Card>

        {/* Created By */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-purple-50/30 dark:to-purple-950/30">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-950/50">
                <User className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {requestDictionary?.fields?.requester || (lang === 'fr' ? 'Créé par' : 'Created By')}
                </CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-purple-700 truncate">
              {request.requester?.name ||
                request.requester?.email?.split("@")[0] ||
                requestDictionary?.notSpecified ||
                (lang === 'fr' ? 'Non spécifié' : 'Not specified')}
            </div>
          </CardContent>
        </Card>

        {/* Created Date */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-orange-50/30 dark:to-orange-950/30">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-950/50">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {requestDictionary?.fields?.createdAt || (lang === 'fr' ? 'Créé le' : 'Created')}
                </CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-orange-700">
              {createdAt.split(",")[0]}
              <div className="text-sm text-orange-600/80 mt-1 font-normal">
                {createdAt.split(",")[1]?.trim() || ""}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Request Details */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 border border-primary/20">
              <Sparkles className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">
                {requestDictionary?.details || (lang === 'fr' ? 'Détails de la demande' : 'Request Details')}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {lang === 'fr' ? 'Informations complètes de la demande' : 'Complete request information'}
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <RequestDetail request={request} dictionary={dictionary} lang={lang} canEdit={canEdit} />
        </CardContent>
      </Card>
    </div>
  );
}
