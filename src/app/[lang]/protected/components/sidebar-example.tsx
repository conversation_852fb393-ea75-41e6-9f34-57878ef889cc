import Link from "next/link";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  LayoutDashboard,
  Users,
  FileText,
  Calendar,
  Settings,
  ClipboardList,
  Bar<PERSON>hart,
} from "lucide-react";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";

// Navigation items with required permissions
const navItems = [
  {
    title: "dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    permission: "dashboard:view", // All users can view dashboard
  },
  {
    title: "contacts",
    href: "/contacts",
    icon: Users,
    permission: "contacts:list", // Only Directors and Coordinators
  },
  {
    title: "requests",
    href: "/requests",
    icon: ClipboardList,
    permission: "requests:list", // All users can view requests
  },
  {
    title: "cases",
    href: "/cases",
    icon: FileText,
    permission: "cases:list", // All users can view cases
  },
  {
    title: "scheduling",
    href: "/scheduling",
    icon: Calendar,
    permission: "schedule:view", // All users can view schedule
  },
  {
    title: "notes",
    href: "/notes",
    icon: FileText,
    permission: "notes:list", // All users can view notes
  },
  {
    title: "reports",
    href: "/reports",
    icon: BarChart,
    permission: "reports:view", // Only Directors and Coordinators
  },
  {
    title: "settings",
    href: "/settings",
    icon: Settings,
    permission: "settings:view", // Only Directors and Coordinators
  },
  {
    title: "admin",
    href: "/admin",
    icon: Settings,
    permission: "admin:dashboard:view", // Only Directors
  },
];

export type SidebarDictionary = {
  navigation?: Record<string, string>;
};

/**
 * Example of a server component sidebar that uses AuthorizationRequired
 * to conditionally render menu items based on user permissions
 */
export async function ServerSidebarExample({
  lang,
  dictionary,
  pathname,
}: {
  lang: string;
  dictionary: SidebarDictionary;
  pathname: string;
}) {
  return (
    <div className="flex flex-col h-full">
      <div className="p-6">
        <h2 className="text-xl font-bold">Si Simple 2025</h2>
      </div>

      <ScrollArea className="flex-1">
        <nav className="px-4 py-2 space-y-1">
          {/* Map through nav items and conditionally render based on permissions */}
          {navItems.map((item) => {
            const Icon = item.icon;

            return (
              <AuthorizationRequired key={item.href} permission={item.permission}>
                <Link
                  href={`/${lang}/protected${item.href}`}
                  className={cn(
                    "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium",
                    pathname.includes(item.href)
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  <span>{dictionary.navigation?.[item.title] || item.title}</span>
                </Link>
              </AuthorizationRequired>
            );
          })}
        </nav>
      </ScrollArea>
    </div>
  );
}
