import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { P } from "@/components/typography";
import { Plus, Search, Sparkles, FileText, Activity, Users, Target } from "lucide-react";
import Link from "next/link";
import { getCaseFileStatistics, getRecentCaseFiles } from "./actions";
import { CaseFileStats, CaseFileCard } from "./components";
import { PageHeader } from "@/components/PageHeader";

interface CaseFilePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Case File Domain Index Page
 * Lists all case files and provides navigation to different states
 */
export default async function CaseFilePage({ params }: CaseFilePageProps) {
  const { lang } = await params;

  // Get statistics and recent case files
  const [statisticsResponse, recentCaseFilesResponse] = await Promise.all([
    getCaseFileStatistics(),
    getRecentCaseFiles(10),
  ]);

  // Use fallback data if requests fail
  const statistics =
    statisticsResponse.success && statisticsResponse.data
      ? statisticsResponse.data
      : {
          opening: 0,
          active: 0,
          suspended: 0,
          closed: 0,
          total: 0,
        };

  const recentCaseFiles =
    recentCaseFilesResponse.success && recentCaseFilesResponse.data
      ? recentCaseFilesResponse.data.map((caseFile) => ({
          id: caseFile.id,
          case_number: caseFile.case_number,
          status: caseFile.status,
          created_at: caseFile.created_at,
          activated_at: caseFile.activated_at,
          suspended_at: caseFile.suspended_at,
          closed_at: caseFile.closed_at,
          assigned_to: caseFile.assigned_to,
          primaryContactName: undefined,
          totalContacts: 0,
          upcomingAppointments: 0,
          pendingDocuments: 0,
        }))
      : [];

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary/10 via-primary/5 to-background border border-primary/20 p-8">
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />
        <div className="relative">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex items-center gap-4">
              <div className="flex h-16 w-16 items-center justify-center rounded-xl bg-primary/10 border border-primary/20">
                <FileText className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold tracking-tight mb-2">
                  {lang === 'fr' ? 'Dossiers de cas' : 'Case Files'}
                </h1>
                <p className="text-lg text-muted-foreground">
                  {lang === 'fr'
                    ? 'Gérer les dossiers de services familiaux et suivre les progrès'
                    : 'Manage family service case files and track progress'
                  }
                </p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                className="group hover:bg-primary/5 hover:border-primary/30"
                asChild
              >
                <Link href={`/${lang}/protected/case-file/list`}>
                  <Search className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                  {lang === 'fr' ? 'Rechercher' : 'Search Cases'}
                </Link>
              </Button>
              <Button
                className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg"
                asChild
              >
                <Link href={`/${lang}/protected/case-file/create`}>
                  <Plus className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                  {lang === 'fr' ? 'Nouveau Dossier' : 'New Case File'}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <CaseFileStats statistics={statistics} lang={lang} />

      {/* Enhanced Recent Case Files */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 border border-primary/20">
              <Activity className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">
                {lang === 'fr' ? 'Dossiers récents' : 'Recent Case Files'}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {lang === 'fr'
                  ? 'Dossiers de cas récemment créés ou modifiés'
                  : 'Recently created or modified case files'
                }
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentCaseFiles && recentCaseFiles.length > 0 ? (
              recentCaseFiles.map((caseFile) => (
                <CaseFileCard key={caseFile.id} caseFile={caseFile} lang={lang} />
              ))
            ) : (
              <div className="text-center py-16">
                <div className="flex h-20 w-20 items-center justify-center rounded-full bg-muted/50 mx-auto mb-6">
                  <FileText className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  {lang === 'fr' ? 'Aucun dossier trouvé' : 'No Case Files Found'}
                </h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  {lang === 'fr'
                    ? 'Aucun dossier de cas n\'a encore été créé. Commencez par créer votre premier dossier.'
                    : 'No case files have been created yet. Get started by creating your first case file.'
                  }
                </p>
                <Button
                  className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg"
                  asChild
                >
                  <Link href={`/${lang}/protected/case-file/create`}>
                    <Plus className="h-4 w-4 mr-2" />
                    {lang === 'fr' ? 'Créer le premier dossier' : 'Create First Case File'}
                  </Link>
                </Button>
              </div>
            )}
          </div>

          {recentCaseFiles && recentCaseFiles.length > 0 && (
            <div className="mt-6 pt-4 border-t">
              <Button variant="outline" className="w-full group" asChild>
                <Link href={`/${lang}/protected/case-file/list`}>
                  {lang === 'fr' ? 'Voir tous les dossiers' : 'View All Case Files'}
                  <Target className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
