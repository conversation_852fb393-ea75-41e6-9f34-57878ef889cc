import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { P } from "@/components/typography";
import { Search, FileText, Activity, Users, Target, TrendingUp, Calendar, Clock, BarChart3, CheckCircle } from "lucide-react";
import Link from "next/link";
import { getCaseFileStatistics, getRecentCaseFiles } from "./actions";
import { CaseFileStats, CaseFileCard } from "./components";
import { PageHeader } from "@/components/PageHeader";

interface CaseFilePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Case File Domain Index Page
 * Lists all case files and provides navigation to different states
 */
export default async function CaseFilePage({ params }: CaseFilePageProps) {
  const { lang } = await params;

  // Get statistics and recent case files
  const [statisticsResponse, recentCaseFilesResponse] = await Promise.all([
    getCaseFileStatistics(),
    getRecentCaseFiles(10),
  ]);

  // Use fallback data if requests fail
  const statistics =
    statisticsResponse.success && statisticsResponse.data
      ? statisticsResponse.data
      : {
          opening: 0,
          active: 0,
          suspended: 0,
          closed: 0,
          total: 0,
        };

  const recentCaseFiles =
    recentCaseFilesResponse.success && recentCaseFilesResponse.data
      ? recentCaseFilesResponse.data.map((caseFile) => ({
          id: caseFile.id,
          case_number: caseFile.case_number,
          status: caseFile.status,
          created_at: caseFile.created_at,
          activated_at: caseFile.activated_at,
          suspended_at: caseFile.suspended_at,
          closed_at: caseFile.closed_at,
          assigned_to: caseFile.assigned_to,
          primaryContactName: undefined,
          totalContacts: 0,
          upcomingAppointments: 0,
          pendingDocuments: 0,
        }))
      : [];

  return (
    <div className="space-y-8">
      {/* Clean Professional Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 border border-primary/20">
            <BarChart3 className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {lang === 'fr' ? 'Tableau de bord des dossiers' : 'Case Files Dashboard'}
            </h1>
            <p className="text-muted-foreground">
              {lang === 'fr'
                ? 'Vue d\'ensemble et gestion des dossiers de cas'
                : 'Overview and management of case files'
              }
            </p>
          </div>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            className="group"
            asChild
          >
            <Link href={`/${lang}/protected/case-file/list`}>
              <Search className="h-4 w-4 mr-2" />
              {lang === 'fr' ? 'Rechercher' : 'Search Cases'}
            </Link>
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <CaseFileStats statistics={statistics} lang={lang} />

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Case Files Trend Chart */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
                  <TrendingUp className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg">
                    {lang === 'fr' ? 'Tendance des dossiers' : 'Case Files Trend'}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {lang === 'fr' ? 'Évolution mensuelle' : 'Monthly progression'}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {lang === 'fr' ? 'Graphique des tendances' : 'Trend Chart'}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {lang === 'fr' ? 'Données de démonstration' : 'Demo data visualization'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions & Recent Activity */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
                  <Target className="h-4 w-4 text-primary" />
                </div>
                <CardTitle className="text-lg">
                  {lang === 'fr' ? 'Actions rapides' : 'Quick Actions'}
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/${lang}/protected/case-file/list`}>
                  <Search className="h-4 w-4 mr-2" />
                  {lang === 'fr' ? 'Rechercher des dossiers' : 'Search Case Files'}
                </Link>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/${lang}/protected/case-file/list?status=active`}>
                  <Activity className="h-4 w-4 mr-2" />
                  {lang === 'fr' ? 'Dossiers actifs' : 'Active Cases'}
                </Link>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/${lang}/protected/appointments`}>
                  <Calendar className="h-4 w-4 mr-2" />
                  {lang === 'fr' ? 'Rendez-vous' : 'Appointments'}
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
                  <Clock className="h-4 w-4 text-primary" />
                </div>
                <CardTitle className="text-lg">
                  {lang === 'fr' ? 'Activité récente' : 'Recent Activity'}
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/20">
                  <div className="flex h-6 w-6 items-center justify-center rounded bg-emerald-100">
                    <Activity className="h-3 w-3 text-emerald-600" />
                  </div>
                  <div className="flex-1 text-sm">
                    <p className="font-medium">
                      {lang === 'fr' ? 'Nouveau dossier créé' : 'New case file created'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {lang === 'fr' ? 'Il y a 2 heures' : '2 hours ago'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/20">
                  <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-100">
                    <Calendar className="h-3 w-3 text-blue-600" />
                  </div>
                  <div className="flex-1 text-sm">
                    <p className="font-medium">
                      {lang === 'fr' ? 'Rendez-vous planifié' : 'Appointment scheduled'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {lang === 'fr' ? 'Il y a 4 heures' : '4 hours ago'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/20">
                  <div className="flex h-6 w-6 items-center justify-center rounded bg-green-100">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                  </div>
                  <div className="flex-1 text-sm">
                    <p className="font-medium">
                      {lang === 'fr' ? 'Dossier fermé' : 'Case file closed'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {lang === 'fr' ? 'Hier' : 'Yesterday'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Case Files */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 border border-primary/20">
              <Activity className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">
                {lang === 'fr' ? 'Dossiers récents' : 'Recent Case Files'}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {lang === 'fr'
                  ? 'Dossiers de cas récemment créés ou modifiés'
                  : 'Recently created or modified case files'
                }
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentCaseFiles && recentCaseFiles.length > 0 ? (
              recentCaseFiles.map((caseFile) => (
                <CaseFileCard key={caseFile.id} caseFile={caseFile} lang={lang} />
              ))
            ) : (
              <div className="text-center py-12">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-muted/50 mx-auto mb-4">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'fr' ? 'Aucun dossier récent' : 'No Recent Case Files'}
                </h3>
                <p className="text-muted-foreground text-sm">
                  {lang === 'fr'
                    ? 'Les dossiers récents apparaîtront ici'
                    : 'Recent case files will appear here'
                  }
                </p>
              </div>
            )}
          </div>

          {recentCaseFiles && recentCaseFiles.length > 0 && (
            <div className="mt-6 pt-4 border-t">
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/${lang}/protected/case-file/list`}>
                  {lang === 'fr' ? 'Voir tous les dossiers' : 'View All Case Files'}
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
