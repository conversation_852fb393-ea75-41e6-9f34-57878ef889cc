"use client";

import { useState, useActionState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { showNotification } from "@/components/ui/notification";
import { editCaseFileObservationNote } from "../actions/edit";

interface EditCaseFileObservationNoteFormProps {
  lang: string;
  caseFileId: string;
  note: {
    id: string;
    title: string;
    content: string;
    status: string;
    attached_to_id: string;
  };
  appointments: Array<{
    id: string;
    title: string;
    appointment_date: string;
    case_number: string;
  }>;
}

export default function EditCaseFileObservationNoteForm({
  lang,
  caseFileId,
  note,
  appointments,
}: EditCaseFileObservationNoteFormProps) {
  const [selectedAppointment, setSelectedAppointment] = useState<string>(note.attached_to_id || "");
  const [title, setTitle] = useState<string>(note.title || "");
  const [content, setContent] = useState<string>(note.content || "");
  const [status, setStatus] = useState<string>(note.status || "draft");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [state, formAction] = useActionState(editCaseFileObservationNote, {
    success: false,
    error: "",
    data: null,
  });

  const handleSubmit = async (formData: FormData) => {
    if (!selectedAppointment) {
      showNotification({
        title: "Validation Error",
        description: "Please select an appointment",
        type: "error",
      });
      return;
    }

    if (!title.trim()) {
      showNotification({
        title: "Validation Error",
        description: "Please enter a title",
        type: "error",
      });
      return;
    }

    if (!content.trim()) {
      showNotification({
        title: "Validation Error",
        description: "Please enter content for the note",
        type: "error",
      });
      return;
    }

    setIsSubmitting(true);

    // Add the form data
    formData.append("lang", lang);
    formData.append("caseFileId", caseFileId);
    formData.append("id", note.id);
    formData.append("appointment_id", selectedAppointment);
    formData.append("title", title);
    formData.append("content", content);
    formData.append("status", status);

    try {
      formAction(formData);
    } catch (error) {
      showNotification({
        title: "Error",
        description: "Failed to update observation note",
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show error notification if there's an error
  if (state.error) {
    showNotification({
      title: "Error",
      description: state.error,
      type: "error",
    });
  }

  return (
    <form action={handleSubmit} className="space-y-6">
      {/* Appointment Selection */}
      <div className="space-y-2">
        <Label htmlFor="appointment">Appointment *</Label>
        <Select value={selectedAppointment} onValueChange={setSelectedAppointment}>
          <SelectTrigger>
            <SelectValue placeholder="Select an appointment..." />
          </SelectTrigger>
          <SelectContent>
            {appointments.map((appointment) => (
              <SelectItem key={appointment.id} value={appointment.id}>
                <div className="flex flex-col">
                  <span className="font-medium">{appointment.title}</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(appointment.appointment_date).toLocaleDateString()}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Title */}
      <div className="space-y-2">
        <Label htmlFor="title">Title *</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter a title for the observation note..."
          maxLength={255}
          required
        />
      </div>

      {/* Status */}
      <div className="space-y-2">
        <Label htmlFor="status">Status</Label>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="pending_approval">Pending Approval</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Content */}
      <div className="space-y-2">
        <Label htmlFor="content">Content *</Label>
        <RichTextEditor
          initialContent={content}
          onChange={setContent}
          placeholder="Write your observation notes here..."
          showToolbar
          showBubbleMenu
          height="400px"
        />
      </div>

      {/* Submit Button */}
      <div className="flex justify-end gap-2">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Updating..." : "Update Note"}
        </Button>
      </div>
    </form>
  );
}
