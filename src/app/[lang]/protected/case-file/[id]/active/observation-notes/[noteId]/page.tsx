import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DocumentPreview } from "@/components/ui/rich-text-editor";
import { ArrowLeft, Edit, Calendar, FileText, User, Clock } from "lucide-react";
import { viewObservationNote } from "../../../../../observation-notes/actions/view";
import { formatDistanceToNow } from "date-fns";
import { getDictionary } from "@/lib/i18n/cache";

interface ViewCaseFileObservationNotePageProps {
  params: Promise<{
    lang: string;
    id: string; // case file id
    noteId: string; // observation note id
  }>;
}

export default async function ViewCaseFileObservationNotePage({
  params,
}: ViewCaseFileObservationNotePageProps) {
  const { lang, id: caseFileId, noteId } = await params;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Suspense fallback={<div>Loading note...</div>}>
        <CaseFileObservationNoteView lang={lang} caseFileId={caseFileId} noteId={noteId} />
      </Suspense>
    </div>
  );
}

async function CaseFileObservationNoteView({
  lang,
  caseFileId,
  noteId,
}: {
  lang: string;
  caseFileId: string;
  noteId: string;
}) {
  const result = await viewObservationNote(
    { success: false, error: "", data: null },
    { id: noteId }
  );
  const dictionary = await getDictionary();
  const t = dictionary.observationNotes || {
    view: {
      backToCase: "Back to Case File",
      edit: "Edit",
      noteInformation: "Note Information",
      observationContent: "Observation Content",
      createdBy: "Created by",
      created: "Created",
      updated: "Updated",
      approvedBy: "Approved by",
      approved: "Approved",
    },
  };

  if (!result.success || !result.data) {
    if (result.error?.includes("not found")) {
      notFound();
    }
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">Error loading note: {result.error}</p>
          <Button variant="outline" asChild className="mt-4">
            <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=observation-notes`}>
              {t.view.backToCase}
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  const note = result.data;

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=observation-notes`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t.view.backToCase}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{note.title}</h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {note.appointment?.title} -{" "}
                {note.appointment?.appointment_date
                  ? new Date(note.appointment.appointment_date).toLocaleDateString()
                  : "N/A"}
              </div>
              {note.case_file && (
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  Case: {note.case_file.case_number}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge
            variant={
              note.status === "approved"
                ? "default"
                : note.status === "pending_approval"
                  ? "secondary"
                  : note.status === "rejected"
                    ? "destructive"
                    : "outline"
            }
          >
            {note.status.replace("_", " ")}
          </Badge>
          <Button variant="outline" size="sm" asChild>
            <Link
              href={`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/${note.id}/edit`}
            >
              <Edit className="h-4 w-4 mr-2" />
              {t.view.edit}
            </Link>
          </Button>
        </div>
      </div>

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{t.view.noteInformation}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{t.view.createdBy}:</span>
              <span>{note.created_by_user?.email || "Unknown"}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{t.view.created}:</span>
              <span>
                {formatDistanceToNow(new Date(note.created_at || ""), { addSuffix: true })}
              </span>
            </div>
            {note.updated_at && note.updated_at !== note.created_at && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{t.view.updated}:</span>
                <span>{formatDistanceToNow(new Date(note.updated_at), { addSuffix: true })}</span>
              </div>
            )}
            {note.approved_by && note.approved_at && (
              <>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t.view.approvedBy}:</span>
                  <span>{note.approved_by_user?.email || "Unknown"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{t.view.approved}:</span>
                  <span>
                    {formatDistanceToNow(new Date(note.approved_at), { addSuffix: true })}
                  </span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{t.view.observationContent}</CardTitle>
        </CardHeader>
        <CardContent>
          <DocumentPreview
            content={note.content}
            title={note.title}
            description={`Observation note for ${note.appointment?.title || "appointment"}`}
            author={note.created_by_user?.email || "Unknown"}
            date={new Date(note.created_at || "")}
            showHeader={false}
            showFooter={false}
          />
        </CardContent>
      </Card>
    </>
  );
}
