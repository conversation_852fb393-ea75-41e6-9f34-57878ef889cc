import { Suspense } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, FileText, Calendar, User, Eye } from "lucide-react";
import { listObservationNotes } from "../../../../observation-notes/actions/list";
import { formatDistanceToNow } from "date-fns";

interface CaseFileObservationNotesProps {
  caseFileId: string;
  lang: string;
  dictionary: {
    title: string;
    description: string;
    createNote: string;
    noNotes: string;
    noNotesDescription: string;
    viewNote: string;
    createdBy: string;
    createdAt: string;
    status: string;
  };
}

export default async function CaseFileObservationNotes({
  caseFileId,
  lang,
  dictionary,
}: CaseFileObservationNotesProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">{dictionary.title}</h2>
          <p className="text-muted-foreground">{dictionary.description}</p>
        </div>
        <Button asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/new`}>
            <Plus className="h-4 w-4 mr-2" />
            {dictionary.createNote}
          </Link>
        </Button>
      </div>

      {/* Notes List */}
      <Suspense fallback={<div>Loading observation notes...</div>}>
        <ObservationNotesList caseFileId={caseFileId} lang={lang} dictionary={dictionary} />
      </Suspense>
    </div>
  );
}

async function ObservationNotesList({
  caseFileId,
  lang,
  dictionary,
}: {
  caseFileId: string;
  lang: string;
  dictionary: CaseFileObservationNotesProps["dictionary"];
}) {
  // Get observation notes filtered by case file
  const result = await listObservationNotes({ case_file_id: caseFileId });

  if (!result.success) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">Error loading observation notes: {result.error}</p>
        </CardContent>
      </Card>
    );
  }

  const notes = result.data || [];

  if (notes.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">{dictionary.noNotes}</h3>
          <p className="text-muted-foreground mb-4">{dictionary.noNotesDescription}</p>
          <Button asChild>
            <Link href={`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/new`}>
              <Plus className="h-4 w-4 mr-2" />
              {dictionary.createNote}
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      {notes.map((note: any) => (
        <Card key={note.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div className="space-y-1">
                <CardTitle className="text-lg">
                  <Link
                    href={`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/${note.id}`}
                    className="hover:underline"
                  >
                    {note.title}
                  </Link>
                </CardTitle>
                <CardDescription>
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {note.appointment_title} -{" "}
                      {note.appointment_date
                        ? new Date(note.appointment_date).toLocaleDateString()
                        : "N/A"}
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {note.created_by_email || "Unknown"}
                    </div>
                  </div>
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={
                    note.status === "approved"
                      ? "default"
                      : note.status === "pending_approval"
                        ? "secondary"
                        : note.status === "rejected"
                          ? "destructive"
                          : "outline"
                  }
                >
                  {note.status.replace("_", " ")}
                </Badge>
                <Button variant="outline" size="sm" asChild>
                  <Link
                    href={`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/${note.id}`}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    {dictionary.viewNote}
                  </Link>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div
                className="text-sm text-muted-foreground line-clamp-2"
                dangerouslySetInnerHTML={{
                  __html: note.content.replace(/<[^>]*>/g, "").substring(0, 150) + "...",
                }}
              />
              <div className="flex justify-between items-center text-xs text-muted-foreground">
                <span>
                  {dictionary.createdAt}{" "}
                  {formatDistanceToNow(new Date(note.created_at), { addSuffix: true })}
                </span>
                {note.updated_at !== note.created_at && (
                  <span>
                    Updated {formatDistanceToNow(new Date(note.updated_at), { addSuffix: true })}
                  </span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
