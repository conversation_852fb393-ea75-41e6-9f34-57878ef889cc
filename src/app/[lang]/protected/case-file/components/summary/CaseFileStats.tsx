import { Card, CardContent } from "@/components/ui/card";
import { P } from "@/components/typography";
import { FileText, Activity, Pause, CheckCircle, Clock, TrendingUp } from "lucide-react";

interface CaseFileStatsProps {
  statistics: {
    opening: number;
    active: number;
    suspended: number;
    closed: number;
    total: number;
  } | null;
}

interface CaseFileStatsPropsWithLang extends CaseFileStatsProps {
  lang?: string;
}

/**
 * Case File Statistics component
 * Displays overview statistics for case files
 */
export function CaseFileStats({ statistics, lang = 'en' }: CaseFileStatsPropsWithLang) {
  const stats = statistics || {
    opening: 0,
    active: 0,
    suspended: 0,
    closed: 0,
    total: 0,
  };

  const statItems = [
    {
      label: "Total Cases",
      labelFr: "Total des dossiers",
      value: stats.total,
      icon: FileText,
      color: "text-slate-700",
      bgColor: "bg-slate-50",
      description: lang === 'fr' ? 'Tous les dossiers' : 'All case files',
    },
    {
      label: "Active Cases",
      labelFr: "Dossiers actifs",
      value: stats.active,
      icon: Activity,
      color: "text-emerald-700",
      bgColor: "bg-emerald-50",
      description: lang === 'fr' ? 'En cours de traitement' : 'Currently being processed',
    },
    {
      label: "This Month",
      labelFr: "Ce mois-ci",
      value: stats.opening, // Using opening as "new this month" for demo
      icon: TrendingUp,
      color: "text-blue-700",
      bgColor: "bg-blue-50",
      description: lang === 'fr' ? 'Nouveaux dossiers' : 'New cases opened',
    },
    {
      label: "Completed",
      labelFr: "Complétés",
      value: stats.closed,
      icon: CheckCircle,
      color: "text-green-700",
      bgColor: "bg-green-50",
      description: lang === 'fr' ? 'Dossiers fermés' : 'Successfully closed',
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((item) => {
        const IconComponent = item.icon;
        return (
          <Card
            key={item.label}
            className="hover:shadow-md transition-shadow border-0 shadow-sm"
          >
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className={`flex h-12 w-12 items-center justify-center rounded-lg ${item.bgColor}`}>
                  <IconComponent className={`h-6 w-6 ${item.color}`} />
                </div>
                <div className="flex-1">
                  <div className="text-2xl font-bold text-foreground mb-1">
                    {item.value}
                  </div>
                  <div className="text-sm font-medium text-foreground mb-1">
                    {lang === 'fr' ? item.labelFr : item.label}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {item.description}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
