import { Card, CardContent } from "@/components/ui/card";
import { P } from "@/components/typography";
import { FileText, Activity, Pause, CheckCircle, Clock, TrendingUp } from "lucide-react";

interface CaseFileStatsProps {
  statistics: {
    opening: number;
    active: number;
    suspended: number;
    closed: number;
    total: number;
  } | null;
}

interface CaseFileStatsPropsWithLang extends CaseFileStatsProps {
  lang?: string;
}

/**
 * Case File Statistics component
 * Displays overview statistics for case files
 */
export function CaseFileStats({ statistics, lang = 'en' }: CaseFileStatsPropsWithLang) {
  const stats = statistics || {
    opening: 0,
    active: 0,
    suspended: 0,
    closed: 0,
    total: 0,
  };

  const statItems = [
    {
      label: "Opening",
      labelFr: "En ouverture",
      value: stats.opening,
      icon: Clock,
      color: "text-orange-700",
      bgColor: "bg-orange-100",
      gradientFrom: "from-orange-50",
      gradientTo: "to-orange-100/50",
      borderColor: "border-orange-200",
    },
    {
      label: "Active",
      labelFr: "Actifs",
      value: stats.active,
      icon: Activity,
      color: "text-green-700",
      bgColor: "bg-green-100",
      gradientFrom: "from-green-50",
      gradientTo: "to-green-100/50",
      borderColor: "border-green-200",
    },
    {
      label: "Suspended",
      labelFr: "Suspendus",
      value: stats.suspended,
      icon: Pause,
      color: "text-yellow-700",
      bgColor: "bg-yellow-100",
      gradientFrom: "from-yellow-50",
      gradientTo: "to-yellow-100/50",
      borderColor: "border-yellow-200",
    },
    {
      label: "Closed",
      labelFr: "Fermés",
      value: stats.closed,
      icon: CheckCircle,
      color: "text-gray-700",
      bgColor: "bg-gray-100",
      gradientFrom: "from-gray-50",
      gradientTo: "to-gray-100/50",
      borderColor: "border-gray-200",
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((item) => {
        const IconComponent = item.icon;
        return (
          <Card
            key={item.label}
            className={`border-0 shadow-lg bg-gradient-to-br ${item.gradientFrom} ${item.gradientTo} hover:shadow-xl transition-all duration-300 group cursor-pointer`}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`flex h-12 w-12 items-center justify-center rounded-xl ${item.bgColor} border ${item.borderColor} group-hover:scale-110 transition-transform`}>
                  <IconComponent className={`h-6 w-6 ${item.color}`} />
                </div>
                <div className="text-right">
                  <div className={`text-3xl font-bold ${item.color} group-hover:scale-105 transition-transform`}>
                    {item.value}
                  </div>
                </div>
              </div>
              <div className="space-y-1">
                <P className={`text-sm font-medium ${item.color}`}>
                  {lang === 'fr' ? item.labelFr : item.label}
                </P>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <TrendingUp className="h-3 w-3" />
                  <span>
                    {lang === 'fr' ? 'Dossiers' : 'Cases'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
