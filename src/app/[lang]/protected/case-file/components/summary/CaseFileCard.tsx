import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { P } from "@/components/typography";
import { FileText, Users, Calendar, Clock, Eye, ArrowRight, Activity, Pause, CheckCircle } from "lucide-react";
import Link from "next/link";
import { CaseFileSummary } from "../../lib/types";

interface CaseFileCardProps {
  caseFile: CaseFileSummary;
  lang: string;
}

/**
 * Case File Card component for displaying case file summaries
 * Used in lists and dashboard views
 */
export function CaseFileCard({ caseFile, lang }: CaseFileCardProps) {
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "active":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          icon: Activity,
          iconBg: "bg-green-100",
          iconColor: "text-green-600",
          gradient: "from-green-50 to-green-100/50",
          label: lang === 'fr' ? 'Actif' : 'Active'
        };
      case "opening":
        return {
          color: "bg-orange-100 text-orange-800 border-orange-200",
          icon: Clock,
          iconBg: "bg-orange-100",
          iconColor: "text-orange-600",
          gradient: "from-orange-50 to-orange-100/50",
          label: lang === 'fr' ? 'En ouverture' : 'Opening'
        };
      case "suspended":
        return {
          color: "bg-yellow-100 text-yellow-800 border-yellow-200",
          icon: Pause,
          iconBg: "bg-yellow-100",
          iconColor: "text-yellow-600",
          gradient: "from-yellow-50 to-yellow-100/50",
          label: lang === 'fr' ? 'Suspendu' : 'Suspended'
        };
      case "closed":
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          icon: CheckCircle,
          iconBg: "bg-gray-100",
          iconColor: "text-gray-600",
          gradient: "from-gray-50 to-gray-100/50",
          label: lang === 'fr' ? 'Fermé' : 'Closed'
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          icon: FileText,
          iconBg: "bg-gray-100",
          iconColor: "text-gray-600",
          gradient: "from-gray-50 to-gray-100/50",
          label: status
        };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusDate = () => {
    switch (caseFile.status) {
      case "active":
        return caseFile.activated_at
          ? (lang === 'fr' ? `Actif depuis le ${formatDate(caseFile.activated_at)}` : `Active since ${formatDate(caseFile.activated_at)}`)
          : (lang === 'fr' ? 'Actif' : 'Active');
      case "opening":
        return lang === 'fr' ? `En ouverture depuis le ${formatDate(caseFile.created_at)}` : `Opening since ${formatDate(caseFile.created_at)}`;
      case "suspended":
        return caseFile.suspended_at
          ? (lang === 'fr' ? `Suspendu depuis le ${formatDate(caseFile.suspended_at)}` : `Suspended since ${formatDate(caseFile.suspended_at)}`)
          : (lang === 'fr' ? 'Suspendu' : 'Suspended');
      case "closed":
        return caseFile.closed_at
          ? (lang === 'fr' ? `Fermé le ${formatDate(caseFile.closed_at)}` : `Closed on ${formatDate(caseFile.closed_at)}`)
          : (lang === 'fr' ? 'Fermé' : 'Closed');
      default:
        return formatDate(caseFile.created_at);
    }
  };

  const statusInfo = getStatusInfo(caseFile.status);
  const StatusIcon = statusInfo.icon;

  return (
    <Card className={`group border-0 shadow-lg bg-gradient-to-br ${statusInfo.gradient} hover:shadow-xl transition-all duration-300 cursor-pointer`}>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className={`flex h-12 w-12 items-center justify-center rounded-xl ${statusInfo.iconBg} border border-opacity-20 group-hover:scale-110 transition-transform flex-shrink-0`}>
            <StatusIcon className={`h-6 w-6 ${statusInfo.iconColor}`} />
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h3 className="font-bold text-lg text-primary mb-1">{caseFile.case_number}</h3>
                {caseFile.primaryContactName && (
                  <p className="text-sm text-muted-foreground">{caseFile.primaryContactName}</p>
                )}
              </div>
              <Badge className={`${statusInfo.color} font-medium border`}>
                {statusInfo.label}
              </Badge>
            </div>

            <p className="text-sm text-muted-foreground mb-4">{getStatusDate()}</p>

            {/* Enhanced Stats */}
            <div className="flex items-center gap-4 text-sm mb-4">
              {caseFile.totalContacts > 0 && (
                <div className="flex items-center gap-1 p-2 rounded-lg bg-muted/30">
                  <Users className="h-3 w-3 text-blue-600" />
                  <span className="font-medium">
                    {caseFile.totalContacts} {lang === 'fr' ? 'contacts' : 'contacts'}
                  </span>
                </div>
              )}
              {caseFile.upcomingAppointments > 0 && (
                <div className="flex items-center gap-1 p-2 rounded-lg bg-muted/30">
                  <Calendar className="h-3 w-3 text-green-600" />
                  <span className="font-medium">
                    {caseFile.upcomingAppointments} {lang === 'fr' ? 'RDV' : 'appointments'}
                  </span>
                </div>
              )}
              {caseFile.pendingDocuments > 0 && (
                <div className="flex items-center gap-1 p-2 rounded-lg bg-muted/30">
                  <FileText className="h-3 w-3 text-yellow-600" />
                  <span className="font-medium text-yellow-700">
                    {caseFile.pendingDocuments} {lang === 'fr' ? 'en attente' : 'pending'}
                  </span>
                </div>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">
                {lang === 'fr' ? 'Créé le' : 'Created'} {formatDate(caseFile.created_at)}
              </div>
              <Button
                variant="outline"
                size="sm"
                className="group/btn hover:bg-primary hover:text-white"
                asChild
              >
                <Link href={`/${lang}/protected/case-file/${caseFile.id}`}>
                  <Eye className="h-4 w-4 mr-2 group-hover/btn:scale-110 transition-transform" />
                  {lang === 'fr' ? 'Voir' : 'View'}
                  <ArrowRight className="h-4 w-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
