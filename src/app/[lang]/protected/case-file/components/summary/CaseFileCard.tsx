import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { P } from "@/components/typography";
import { FileText, Users, Calendar, Clock, Eye, ArrowRight, Activity, Pause, CheckCircle } from "lucide-react";
import Link from "next/link";
import { CaseFileSummary } from "../../lib/types";

interface CaseFileCardProps {
  caseFile: CaseFileSummary;
  lang: string;
}

/**
 * Case File Card component for displaying case file summaries
 * Used in lists and dashboard views
 */
export function CaseFileCard({ caseFile, lang }: CaseFileCardProps) {
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "active":
        return {
          color: "bg-emerald-100 text-emerald-800",
          icon: Activity,
          iconBg: "bg-emerald-50",
          iconColor: "text-emerald-600",
          label: lang === 'fr' ? 'Actif' : 'Active'
        };
      case "opening":
        return {
          color: "bg-blue-100 text-blue-800",
          icon: Clock,
          iconBg: "bg-blue-50",
          iconColor: "text-blue-600",
          label: lang === 'fr' ? 'En ouverture' : 'Opening'
        };
      case "suspended":
        return {
          color: "bg-amber-100 text-amber-800",
          icon: Pause,
          iconBg: "bg-amber-50",
          iconColor: "text-amber-600",
          label: lang === 'fr' ? 'Suspendu' : 'Suspended'
        };
      case "closed":
        return {
          color: "bg-slate-100 text-slate-800",
          icon: CheckCircle,
          iconBg: "bg-slate-50",
          iconColor: "text-slate-600",
          label: lang === 'fr' ? 'Fermé' : 'Closed'
        };
      default:
        return {
          color: "bg-slate-100 text-slate-800",
          icon: FileText,
          iconBg: "bg-slate-50",
          iconColor: "text-slate-600",
          label: status
        };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusDate = () => {
    switch (caseFile.status) {
      case "active":
        return caseFile.activated_at
          ? (lang === 'fr' ? `Actif depuis le ${formatDate(caseFile.activated_at)}` : `Active since ${formatDate(caseFile.activated_at)}`)
          : (lang === 'fr' ? 'Actif' : 'Active');
      case "opening":
        return lang === 'fr' ? `En ouverture depuis le ${formatDate(caseFile.created_at)}` : `Opening since ${formatDate(caseFile.created_at)}`;
      case "suspended":
        return caseFile.suspended_at
          ? (lang === 'fr' ? `Suspendu depuis le ${formatDate(caseFile.suspended_at)}` : `Suspended since ${formatDate(caseFile.suspended_at)}`)
          : (lang === 'fr' ? 'Suspendu' : 'Suspended');
      case "closed":
        return caseFile.closed_at
          ? (lang === 'fr' ? `Fermé le ${formatDate(caseFile.closed_at)}` : `Closed on ${formatDate(caseFile.closed_at)}`)
          : (lang === 'fr' ? 'Fermé' : 'Closed');
      default:
        return formatDate(caseFile.created_at);
    }
  };

  const statusInfo = getStatusInfo(caseFile.status);
  const StatusIcon = statusInfo.icon;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className={`flex h-10 w-10 items-center justify-center rounded-lg ${statusInfo.iconBg}`}>
            <StatusIcon className={`h-5 w-5 ${statusInfo.iconColor}`} />
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-foreground">{caseFile.case_number}</h3>
              <Badge className={`${statusInfo.color} text-xs`}>
                {statusInfo.label}
              </Badge>
            </div>

            {caseFile.primaryContactName && (
              <p className="text-sm text-muted-foreground mb-2">{caseFile.primaryContactName}</p>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                {caseFile.totalContacts > 0 && (
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {caseFile.totalContacts}
                  </span>
                )}
                {caseFile.upcomingAppointments > 0 && (
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {caseFile.upcomingAppointments}
                  </span>
                )}
                <span>{formatDate(caseFile.created_at)}</span>
              </div>

              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2"
                asChild
              >
                <Link href={`/${lang}/protected/case-file/${caseFile.id}`}>
                  <Eye className="h-4 w-4 mr-1" />
                  {lang === 'fr' ? 'Voir' : 'View'}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
