"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Calendar, FileText, Edit, Eye, Zap, ArrowRight, Phone, Mail, MessageSquare, UserPlus, ClipboardList, Bell } from "lucide-react";
import { useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface QuickActionsProps {
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    schedule: string;
    addDocument: string;
    createNote: string;
    viewHistory: string;
  };
}

/**
 * Quick Actions component for case file dashboard
 * Provides shortcuts to common case file management tasks
 */
export function QuickActions({ caseFileId, lang: _lang, dictionary }: QuickActionsProps) {
  // Default translations
  const defaultDictionary = {
    title: "Quick Actions",
    schedule: "Schedule Appointment",
    addDocument: "Add Document",
    createNote: "Create Note",
    viewHistory: "View History",
  };

  const t = dictionary || defaultDictionary;
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Handle file upload for Add Document button
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      setIsUploading(true);

      for (const file of Array.from(files)) {
        const formData = new FormData();
        formData.append("files", file);
        formData.append("attached_to_type", "case_file");
        formData.append("attached_to_id", caseFileId);
        formData.append("name", file.name);

        const response = await fetch("/api/documents/upload", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }
      }

      toast.success(`Successfully uploaded ${files.length} document(s)`);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      // Refresh the page to update document lists
      router.refresh();
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error("Failed to upload documents");
    } finally {
      setIsUploading(false);
    }
  };

  // Handle View History button click
  const handleViewHistory = () => {
    const historyElement = document.getElementById("case-file-history");
    if (historyElement) {
      historyElement.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };
  return (
    <div className="relative">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 border border-primary/20">
          <Zap className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">{t.title}</h3>
          <p className="text-sm text-muted-foreground">
            {_lang === 'fr' ? 'Actions rapides pour ce dossier' : 'Quick actions for this case file'}
          </p>
        </div>
      </div>

      {/* Hidden file input for document upload */}
      <Input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={handleFileUpload}
        id="quick-action-document-upload"
      />

      {/* Compact Quick Actions */}
      <div className="flex flex-wrap gap-3">
        {/* Schedule Appointment */}
        <Button
          variant="outline"
          size="sm"
          className="group hover:bg-green-50 hover:border-green-200 transition-colors"
        >
          <Calendar className="h-4 w-4 mr-2 text-green-600" />
          {_lang === 'fr' ? 'Planifier RDV' : 'Schedule'}
        </Button>

        {/* Add Document */}
        <Button
          variant="outline"
          size="sm"
          className="group hover:bg-blue-50 hover:border-blue-200 transition-colors"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          <FileText className="h-4 w-4 mr-2 text-blue-600" />
          {isUploading ? (_lang === 'fr' ? 'Envoi...' : 'Uploading...') : (_lang === 'fr' ? 'Ajouter Document' : 'Add Document')}
        </Button>

        {/* Create Note */}
        <Button
          variant="outline"
          size="sm"
          className="group hover:bg-purple-50 hover:border-purple-200 transition-colors"
        >
          <Edit className="h-4 w-4 mr-2 text-purple-600" />
          {_lang === 'fr' ? 'Créer Note' : 'Create Note'}
        </Button>

        {/* Add Contact */}
        <Button
          variant="outline"
          size="sm"
          className="group hover:bg-orange-50 hover:border-orange-200 transition-colors"
        >
          <UserPlus className="h-4 w-4 mr-2 text-orange-600" />
          {_lang === 'fr' ? 'Ajouter Contact' : 'Add Contact'}
        </Button>
      </div>
    </div>
  );
}
